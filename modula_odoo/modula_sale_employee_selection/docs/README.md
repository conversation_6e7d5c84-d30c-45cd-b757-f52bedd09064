# Employee Selection Module Documentation

## 📋 **Documentation Overview**

This documentation provides comprehensive guidance for AI-assisted development of the Employee Selection module and future Odoo 18 development.

## 🎯 **Start Here for AI Handoff**

### **Primary Document**
- **[AI_HANDOFF_DOCUMENT.md](AI_HANDOFF_DOCUMENT.md)** - **START HERE**
  - Complete module overview and current state
  - Critical implementation patterns and gotchas
  - Architecture and workflow documentation
  - Essential for understanding the module

## 📚 **Development Guidelines (Odoo18/)**

### **Core Guidelines**
- **[ODOO18_JAVASCRIPT_GUIDELINES.md](Odoo18/ODOO18_JAVASCRIPT_GUIDELINES.md)**
  - JavaScript patterns and best practices
  - Form state management (critical: _askChanges() vs save())
  - DOM manipulation for UI control
  - Service usage and error handling
  - **Updated with real experience from this project**

- **[odoo_python_development_guidelines.md](Odoo18/odoo_python_development_guidelines.md)**
  - Python development patterns for Odoo 18
  - Template method pattern implementation
  - Breaking changes and migration guidelines
  - **Updated with template method pattern experience**

- **[ODOO18_XML_TEMPLATE_GUIDELINES.md](Odoo18/ODOO18_XML_TEMPLATE_GUIDELINES.md)**
  - XML template development patterns
  - Component template structure
  - Template naming conventions

- **[odoo_xml_view_guide_line.md](Odoo18/odoo_xml_view_guide_line.md)**
  - XML view development guidelines
  - Form view patterns and best practices
  - View inheritance and customization

## 🤖 **AI Development Support**

### **AI Workflow Documents**
- **[AI_DEVELOPMENT_WORKFLOW.md](AI_DEVELOPMENT_WORKFLOW.md)**
  - Step-by-step AI development process
  - Context management and recovery strategies
  - Best practices for AI-assisted development

- **[AI_PROMPT_TEMPLATES.md](AI_PROMPT_TEMPLATES.md)**
  - Ready-to-use prompt templates
  - Context-aware prompting strategies
  - Specific prompts for different development tasks

## 🧪 **Testing and Quality**

### **Testing Documentation**
- **[TESTING_INSTRUCTIONS.md](TESTING_INSTRUCTIONS.md)**
  - Comprehensive testing procedures
  - Functional and technical test cases
  - Verification checklists

## 🎯 **Quick Reference for AI**

### **Critical Patterns from This Project**

#### **1. Form State Management**
```javascript
// ✅ CRITICAL: Preserve field values without saving
await this.model._askChanges(); // Commits to memory only

// ❌ WRONG: This saves to backend
await this.model.root.save();
```

#### **2. Button Visibility Control**
```javascript
// ✅ CORRECT: DOM manipulation for immediate feedback
document.getElementsByName('action_name')[0].style.display = 'none';

// ❌ WRONG: Backend changes won't persist without save
await orm.call("model", "write", [[id], {field: value}]);
```

#### **3. Template Method Pattern**
```python
# Base module - template method
def _get_employee_is_show(self, employee_data):
    return True  # Default behavior

# Dependent module - override
def _get_employee_is_show(self, employee_data):
    # Specific business logic
    return employee.job_id.name == 'Store Manager'
```

### **Essential Architecture**
- **Base Module**: `modula_sale_employee_selection` (template/infrastructure)
- **Dependent Module**: `modula_sale` (business logic)
- **Pattern**: Template method for extensibility
- **Frontend**: Reactive button implementation with template inheritance
- **Workflow**: No-save approval process with manual save control
- **Components**: Employee hooks service, float field triggers, reactive buttons

## 📁 **Documentation Structure**

```
docs/
├── README.md                           # This file - start here
├── AI_HANDOFF_DOCUMENT.md             # 🎯 PRIMARY - Complete handoff
├── AI_DEVELOPMENT_WORKFLOW.md         # AI development process
├── AI_PROMPT_TEMPLATES.md             # Ready-to-use prompts
├── TESTING_INSTRUCTIONS.md            # Testing procedures
└── Odoo18/                            # Development guidelines
    ├── ODOO18_JAVASCRIPT_GUIDELINES.md    # JS patterns (updated)
    ├── odoo_python_development_guidelines.md # Python patterns (updated)
    ├── ODOO18_XML_TEMPLATE_GUIDELINES.md  # XML templates
    └── odoo_xml_view_guide_line.md        # XML views
```

## 🚀 **For Future AI Development**

### **Before Starting Development**
1. **Read [AI_HANDOFF_DOCUMENT.md](AI_HANDOFF_DOCUMENT.md)** - Complete context
2. **Review relevant guidelines** in `Odoo18/` folder
3. **Check [TESTING_INSTRUCTIONS.md](TESTING_INSTRUCTIONS.md)** for verification
4. **Use [AI_PROMPT_TEMPLATES.md](AI_PROMPT_TEMPLATES.md)** for efficient prompting

### **Key Success Factors**
- ✅ **Understand form state management** (save vs preserve)
- ✅ **Follow template method pattern** for extensibility
- ✅ **Use DOM manipulation** for UI changes in no-save workflows
- ✅ **Reference @odoo/addons/web/** for base components
- ✅ **Test thoroughly** using provided test cases

### **Common Pitfalls to Avoid**
- ❌ **Don't save forms** in approval workflows (use _askChanges())
- ❌ **Don't rely on backend state** for UI changes in no-save workflows
- ❌ **Don't close popups** before executing callback actions
- ❌ **Don't break template method pattern** when extending functionality

## 📞 **Support and Context**

### **Module Status**
- ✅ **Production Ready**: All business requirements met
- ✅ **Fully Tested**: Comprehensive test coverage
- ✅ **Well Documented**: Complete AI handoff documentation
- ✅ **Extensible**: Template method pattern for future development

### **Development Context**
This module was developed through extensive AI-assisted development with multiple iterations and refinements. The documentation captures real-world experience and proven patterns that work reliably in Odoo 18.

---

**For AI Developers**: Start with [AI_HANDOFF_DOCUMENT.md](AI_HANDOFF_DOCUMENT.md) for complete context, then refer to specific guidelines in `Odoo18/` folder as needed.
