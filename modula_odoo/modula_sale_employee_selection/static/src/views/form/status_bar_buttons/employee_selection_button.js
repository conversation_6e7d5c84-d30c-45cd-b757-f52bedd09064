

import { StatusBarButtons } from "@web/views/form/status_bar_buttons/status_bar_buttons";
import { patch } from "@web/core/utils/patch";
import { pickUseConnectedEmployee } from "../../../employee_selection/employee_hooks";
import { useEffect, useState } from "@odoo/owl";
import { useBus } from "@web/core/utils/hooks";

patch(StatusBarButtons.prototype, {

    setup() {
        super.setup();
        this.modelAllowed = ['sale.order', 'stock.picking'];
        if (this.modelAllowed.includes(this.env.model?.root.resModel)) {
            this.useEmployee = pickUseConnectedEmployee("form", this.props.context, this.env);
            this.useEmployee.getConnectedEmployees();

            this.needApprove = useState({ value: false });

            useBus(this.env.model.bus, "update", async () => {
                await this.updateNeedApproveFromBackend();
            });

            useEffect(
                () => {
                    this.updateNeedApproveFromBackend();
                },
                () => [this.env.model.root.data.order_line]
            );
        }
    },

    async updateNeedApproveFromBackend() {
        try {
            if (this.env.model?.root.resModel !== 'sale.order') {
                return;
            }

            const record = this.env.model.root;
            if (!record || !record.data) {
                return;
            }

            // Get current order lines that are dirty or recently changed
            const orderLines = record.data.order_line.records;
            if (!orderLines || !Array.isArray(orderLines) || orderLines.length === 0) {
                this.needApprove.value = false;
                return;
            }

            let needsApproval = false;
            const order_line_data = orderLines.map(line => ({
                id: line.resId,
                product_id: line.data.product_id[0],
                discount: line.data.discount
            }));

            needsApproval = await this.env.services.orm.call(
                "sale.order",
                "need_employee_selection",
                [record.resId],
                {
                    'order_line': order_line_data,
                }
            );

            // Update reactive state
            if (this.needApprove.value !== needsApproval) {
                this.needApprove.value = needsApproval;
                console.log("Backend check - need_approve updated to:", needsApproval);
            }

        } catch (error) {
            console.error("Error checking need_employee_selection:", error);
            // On error, default to showing button if model data suggests it
            this.needApprove.value = record.data.need_approve || false;
        }
    },
    async onPopEmployeeSelection(ev) {
        ev.preventDefault();
        ev.stopPropagation();

        try {
            // Check if this is a sale order
            if (!this.modelAllowed.includes(this.env.model?.root.resModel)) {
                console.warn("Approve button only works on sale orders");
                return;
            }

            // Get the current record
            const record = this.env.model.root;

            // // Check if approval is needed
            // if (!record.data.need_approve) {
            //     console.warn("No approval needed for this sale order");
            //     return;
            // }

            await this.useEmployee.getAllEmployees(this.env.model?.root.resModel, this.env.model?.root.resId);
            if (this.env.model?.root.resModel === 'sale.order') {
                this.useEmployee.filterEmployeesByIsShow();
                this.useEmployee.popupAddEmployee();
            } else if (this.env.model?.root.resModel === 'stock.picking') {
                this.useEmployee.popupAddEmployee();
            }

        } catch (error) {
            console.error("Error in Approve button click:", error);
            this.env.services.notification.add("Error opening employee selection", { type: "danger" });
        }
    },

    get shouldShowApproveButton() {
        if (this.env.model?.root.resModel !== 'sale.order') {
            return false;
        }

        const record = this.env.model.root;
        const modelNeedApprove = record && record.data && record.data.need_approve;
        const stateNeedApprove = this.needApprove?.value;

        const needApprove = this.env.model?.root ? (modelNeedApprove || stateNeedApprove) : false;

        if (needApprove) {
            console.log("shouldShowApproveButton: true - need_approve:", needApprove);
        }

        return needApprove;
    },

    get shouldShowSelectEmployeeButton() {
        if (this.env.model?.root.resModel !== 'stock.picking' || !this.env.model?.root.resId) {
            return false;
        }
        const record = this.env.model.root;
        const needSelectEmployee = record && record.data && !record.data.employee_id;

        if (needSelectEmployee) {
            console.log("shouldShowSelectEmployeeButton: true - need_select_employee:", needSelectEmployee);
        }

        return needSelectEmployee;
    },

    async onSelectEmployeeClick(ev) {
        ev.preventDefault();
        ev.stopPropagation();

        try {
            // Check if this is a sale order
            if (this.env.model?.root.resModel !== 'sale.order') {
                console.warn("Approve button only works on sale orders");
                return;
            }

            // Get the current record
            const record = this.env.model.root;

            // // Check if approval is needed
            // if (!record.data.need_approve) {
            //     console.warn("No approval needed for this sale order");
            //     return;
            // }
            await this.useEmployee.getAllEmployees();
            this.useEmployee.filterEmployeesByIsShow();
            this.useEmployee.popupAddEmployee();

        } catch (error) {
            console.error("Error in Select Employee button click:", error);
            this.env.services.notification.add("Error opening employee selection", { type: "danger" });
        }
    },
});