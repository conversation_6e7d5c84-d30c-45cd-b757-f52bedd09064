import { _t } from "@web/core/l10n/translation";
import { useService } from "@web/core/utils/hooks";
import { browser } from "@web/core/browser/browser";
import { SelectionPopup } from "./popup";
import { PinPopup } from "./pin_popup";
import { DialogWrapper } from "./dialog_wrapper";
import { registry } from "@web/core/registry"; ``
import { useState } from "@odoo/owl";


export function pickUseConnectedEmployee(controllerType, context, workcenterId, env) {

    const orm = useService("orm");
    const notification = useService("notification");
    const dialog = useService("dialog");
    const imageBaseURL = `${browser.location.origin}/web/image?model=hr.employee&field=avatar_128&id=`;

    let formSaveCallbacks = {};
    let pinWasOpened = false;
    let employeeWasSelected = false;


    const employees = useState({
        connected: [],
        all: [],
        admin: {},
    });

    const floorTypes = useState({
        connected: [],
        all: [],
        admin: {},
    });
    const popup = useState({
        PinPopup: {
            isShown: false,
        },
        SelectionPopup: {
            isShown: false,
        },
    });

    const openDialog = (id, component, props) => {
        popup[id] = {
            isShown: true,
            close: dialog.add(
                DialogWrapper,
                {
                    Component: component,
                    componentProps: props,
                },
                {
                    onClose: () => {
                        popup[id] = { isShown: false };
                    },
                }
            ),
        };
    };

    const setFormSaveCallbacks = (callbacks) => {
        formSaveCallbacks = callbacks;
    };

    const getAllEmployees = async (model = null, id = null) => {
        if (model && id) {
            employees.res_model = model;
            employees.res_id = id;
        }
        const res = await orm.call("hr.employee", "get_all_employees", [false]);
        if (res.all) {
            employees.all = res.all;
        } else {
            employees.all = [];
        }
    };

    const filterEmployeesByIsShow = () => {
        if (employees.all && Array.isArray(employees.all)) {
            // Filter employees with is_show = true
            employees.all = employees.all.filter(employee => employee.is_show === true);

            // Add default action_approve_sale_order field to each employee
            employees.all = employees.all.map(employee => ({
                ...employee,
                action_approve_sale_order: true  // Default value for approval action
            }));

            console.log(`Filtered employees: ${employees.all.length} employees with is_show=true and action_approve_sale_order=true`);
        }
    };

    // Add context to call in back-end, no other way to pass context to back-end
    // this should keep at all cost, please do not remove it.
    const getEmployeeContext = (employeeId) => {
        const employee = employees.all.find((e) => e.id === employeeId);
        return {
            action_approve_sale_order: employee && employee.action_approve_sale_order || false,
            res_model: employees.res_model || null,
            res_id: employees.res_id || null,
        };
    };

    // const getAllFloorTypes = async () => {
    //     const fieldsToRead = ["id", "name"];
    //     floorTypes.all = await orm.searchRead("flooring.type", [], fieldsToRead);
    // };

    const hideApproveButton = async () => {
        try {
            const approveButtons = document.getElementsByName('action_approve_sale_order');

            if (approveButtons.length > 0) {
                for (let i = 0; i < approveButtons.length; i++) {
                    approveButtons[i].style.display = 'none';
                }
                console.log("Approve button(s) hidden successfully");
            } else {
                console.warn("No Approve button found with name 'action_approve_sale_order'");
            }

        } catch (error) {
            console.error("Error hiding approve button:", error);
        }
    };

    const showApproveButton = async () => {
        try {
            // Find the "Approve" button by name attribute
            const approveButtons = document.getElementsByName('action_approve_sale_order');

            if (approveButtons.length > 0) {
                // Show all "Approve" buttons found
                for (let i = 0; i < approveButtons.length; i++) {
                    approveButtons[i].style.display = '';
                }
                console.log("Approve button(s) shown successfully");
            } else {
                console.warn("No Approve button found with name 'action_approve_sale_order'");
            }

        } catch (error) {
            console.error("Error showing approve button:", error);
        }
    };

    const selectEmployee = async (employeeId, pin) => {
        const employee = employees.all.find((e) => e.id === employeeId);
        const employee_connected = employees.connected.find((e) => e.name && e.id === employee.id);
        const employee_function = employee_connected ? "logout" : "login";

        const context = getEmployeeContext(employeeId);

        console.log(`Selecting employee ${employeeId} for ${context.res_model} ID: ${context.res_id}`);

        const pinValid = await orm.call("hr.employee", employee_function, [employeeId, pin], { context });
        if (!pinValid && popup.PinPopup.isShown) {
            return notification.add(_t("Wrong password!"), { type: "danger" });
        }
        if (!pinValid) {
            return askPin(employee);
        }

        employeeWasSelected = true;

        if (!pinWasOpened) {
            await hideApproveButton();
        }

        if (employee_function === "login") {
            notification.add(_t("Success!"), { type: "success" });
            await getConnectedEmployees(true);

            // Handle stock picking auto-validation and form refresh
            if (context.res_model === 'stock.picking') {
                console.log("Stock picking form refresh");

                if (formSaveCallbacks && formSaveCallbacks.refreshForm) {
                    try {
                        await formSaveCallbacks.refreshForm();
                        console.log("Stock picking form refreshed after validation");
                    } catch (error) {
                        console.error("Error refreshing stock picking form:", error);
                        notification.add(_t("Error refreshing form"), { type: "danger" });
                    }
                }
            }

            if (controllerType === "kanban") {
                console.log('thing we must do after employee connected login');
            }
        } else {
            // await stopAllWorkorderFromEmployee(employeeId);
            notification.add(_t("Logged out!"), { type: "success" });
        }

        closePopup("SelectionPopup");
        await getConnectedEmployees();
    };

    const getConnectedEmployees = async (login = false) => {
        const res = await orm.call("hr.employee", "get_all_employees", [null, login]);
        if (res.all) {
            employees.all = res.all;
        } else {
            employees.all = [];
        }
        res.connected.sort(function (emp1, emp2) {
            if (emp1.workorder.length == 0) {
                return 1;
            }
            if (emp2.workorder.length == 0) {
                return -1;
            }
            return 0;
        });
        employees.connected = res.connected.map((obj) => {
            const emp = employees.all.find(e => e.id === obj.id);
            return { ...obj, name: emp.name };
        })
        const admin = employees.all.find(e => e.id === res.admin);
        if (admin) {
            employees.admin = {
                name: admin.name,
                id: admin.id,
                path: imageBaseURL + `${admin.id}`,
            }
        } else {
            employees.admin = {};
        }
    };

    // const getConnectedFlooring = async () => {
    //     const res = await orm.call("flooring.type", "get_all_flooring", [null]);
    //     floorTypes.all = res.all;

    //     floorTypes.connected = res.connected.map((obj) => {
    //         const floor = floorTypes.all.find(f => f.id === obj.id);
    //         return { ...obj, name: floor.name };
    //     })
    // };

    const logout = async (employeeId) => {
        const context = getEmployeeContext(employeeId);
        const success = await orm.call("hr.employee", "logout", [employeeId, false, true], { context });
        if (success) {
            notification.add(_t("Logged out!"), { type: "success" });
            // await Promise.all([stopAllWorkorderFromEmployee(employeeId), getConnectedEmployees()]);
        } else {
            notification.add(_t("Error during log out!"), { type: "danger" });
        }
    };

    const askPin = async (employee) => {
        try {
            pinWasOpened = true;

            openDialog("PinPopup", PinPopup, {
                popupData: { employee },
                onClosePopup: async (popupId) => {
                    closePopup(popupId);

                    if (employeeWasSelected) {
                        await hideApproveButton();

                        // Stock picking form refresh after confirm PIN validation
                        const context = getEmployeeContext(employee.id);
                        if (context.res_model === 'stock.picking' && formSaveCallbacks && formSaveCallbacks.refreshForm) {
                            try {
                                console.log("PIN validation completed for stock picking - triggering form refresh");
                                await formSaveCallbacks.refreshForm();
                            } catch (error) {
                                console.error("Error refreshing form after PIN validation:", error);
                                notification.add(_t("Error refreshing form"), { type: "danger" });
                            }
                        }

                        employeeWasSelected = false; // Reset flag
                    }
                },
                onPinValidate: checkPin.bind(this),
            });
        } catch (error) {
            console.error("Error in askPin:", error);
            notification.add(_t("Error opening PIN validation"), { type: "danger" });
        }
    };

    const toggleSessionOwner = async (employee_id, pin) => {
        if (employees.admin.id == employee_id) {
            const context = getEmployeeContext(employee_id);
            await orm.call("hr.employee", "remove_session_owner", [employee_id], { context });
            await getConnectedEmployees();
        } else {
            setSessionOwner(employee_id, pin);
        }
    };

    const setSessionOwner = async (employee_id, pin) => {
        if (employees.admin.id == employee_id && employee_id == employees.connected[0].id) {
            return;
        }
        const context = getEmployeeContext(employee_id);
        const pinValid = await orm.call("hr.employee", "login", [employee_id, pin], { context });

        if (!pinValid) {
            if (pin) {
                notification.add(_t("Wrong password!"), { type: "danger" });
            }
            if (popup.PinPopup.isShown) {
                return;
            }
            askPin({ id: employee_id });
        }
        await getConnectedEmployees();
    };

    // const stopAllWorkorderFromEmployee = async (employeeId) => {
    //     await orm.call("hr.employee", "stop_all_workorder_from_employee", [employeeId]);
    // };

    const popupAddEmployee = async () => {
        try {
            pinWasOpened = false;
            employeeWasSelected = false;

            const list = employees.all.map((employee) =>
                Object.create({
                    id: employee.id,
                    item: employee,
                    label: employee.name,
                    barcode: employee.barcode,
                    isSelected: employees.connected.find((e) => e.id === employee.id),
                })
            );
            openDialog("SelectionPopup", SelectionPopup, {
                popupData: { title: _t("Select Employee"), list: list },
                onClosePopup: async (popupId) => {
                    closePopup(popupId);

                    employeeWasSelected = false;
                },
                onSelectEmployee: selectEmployee.bind(this),
            });
        } catch (error) {
            console.error("Error in popupAddEmployee:", error);
            notification.add(_t("Error opening employee selection"), { type: "danger" });
        }
    };

    const pinValidation = async (employeeId, pin) => {
        const context = getEmployeeContext(employeeId);
        return await orm.call("hr.employee", "pin_validation", [employeeId, pin], { context });
    };

    const checkPin = async (employeeId, pin) => {
        if (
            employees.connected.find((e) => e.id === employeeId) &&
            employees.admin?.id != employeeId
        ) {
            setSessionOwner(employeeId, pin);
        } else {
            selectEmployee(employeeId, pin);
        }
        const context = getEmployeeContext(employeeId);
        const pinValid = await orm.call("hr.employee", "pin_validation", [employeeId, pin], { context });
        return pinValid;
    };

    const closePopup = (popupId) => {
        const { isShown, close } = popup[popupId];
        if (isShown) {
            close();
        }
    };

    const openRecord = async (record, mode) => {
        const id = await orm.call("hr.employee", "get_session_owner", [null]);
        if (id.length == 0) {
            context.openRecord = [record, mode];
            // openEmployeeSelection();
            return;
        }
        delete context.openRecord;
        Object.assign(context, { employees: id });
    };

    return {
        getAllEmployees,
        // getAllFloorTypes,
        getConnectedEmployees,
        // getConnectedFlooring,
        logout,
        askPin,
        setSessionOwner,
        // stopAllWorkorderFromEmployee,
        toggleSessionOwner,
        popupAddEmployee,
        // popupAddFlooringType,
        checkPin,
        closePopup,
        pinValidation,
        selectEmployee,
        openRecord,
        employees,
        popup,
        setFormSaveCallbacks,
        filterEmployeesByIsShow,
    };
}


// Register the "Approve" button client action
registry.category("actions").add("action_approve_sale_order", (env, action) => {
    console.log("action_approve_sale_order triggered", action);
    const useEmployee = pickUseConnectedEmployee("form", {}, [], env);
    return useEmployee.popupAddEmployee();
});
