/** @odoo-module **/

import { Dialog } from "@web/core/dialog/dialog";
import { Component } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class SelectionPopup extends Component {
    static components = { Dialog };

    get title() {
        return this.props.popupData.title;
    }

    get list() {
        return this.props.popupData.list;
    }

    async cancel() {
        await this.props.onClosePopup('SelectionPopup', true);
    }

    async selectItem(id) {
        return this.props.onSelectEmployee(id);
    }

    onSelectEmployee(id) {
        console.log("onSelectEmployee", id);
    }

    async onClosePopup(popupId) {
        console.log("onClosePopup", popupId);
        await this.props.onClosePopup(popupId);
    }

}
SelectionPopup.props = {
    popupData: Object,
    onClosePopup: Function,
    onSelectEmployee: Function,
};
SelectionPopup.template = 'modula_sale_employee_selection.SelectionPopup';
