import { Float<PERSON>ield } from "@web/views/fields/float/float_field";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";

patch(FloatField.prototype, {
    setup() {
        super.setup();
        this.action = useService("action");
        this.orm = useService("orm");
        this.notification = useService("notification");
    },
    async onFocusOut() {
        const value = this.inputRef.el.value;
        const field_name = this.inputRef.el.parentElement.getAttribute('name');
        const parent_model = this.env.model.config.resModel;
        const parent_id = this.env.model.config.resId;
        const o2m_model = this.props.record.resModel;
        const o2m_id = this.props.record.resId;

        if (parent_model === 'sale.order' && this.props.record.dirty) {
            try {
                const record = this.env.model.root;
                if (!record || !record.data) {
                    return;
                }
                const orderLines = record.data.order_line.records;
                const order_line_data = orderLines.map(line => ({
                    id: line.resId,
                    product_id: line.data.product_id[0],
                    discount: line.data.discount
                }));
                const res = await this.orm.call("sale.order", "need_employee_selection",
                    [parent_id || null],
                    { 'order_line': order_line_data }
                );
                console.log("float field onFocusOut need_employee_selection", res)
                if (res) {
                    this.showApproveButton();
                }
                this.env.model.root.data.need_approve = res;

                this.env.model.notify();
                // Do NOT trigger popup here - only set flag
                // The "Approve" button will trigger the popup when clicked
            } catch (error) {
                // Create error on purpose to trigger show button without saving form
                this.env.model.root.data.need_approve = true
                this.env.model.notify();
                console.log("need_approve", this.env.model.root.data.need_approve)
                console.log("Temporary skip error employee selection process")
            }
        } else {
            return super.onFocusOut();
        }
    },
    showApproveButton() {
        try {
            // Find the "Approve" button by name attribute
            const approveButtons = document.getElementsByName('action_approve_sale_order');

            if (approveButtons.length > 0) {
                // Show all "Approve" buttons found
                for (let i = 0; i < approveButtons.length; i++) {
                    approveButtons[i].style.display = '';
                }
                console.log("Approve button(s) shown successfully");
            } else {
                console.warn("No Approve button found with name 'action_approve_sale_order'");
            }

        } catch (error) {
            console.error("Error showing approve button:", error);
        }
    },

});
