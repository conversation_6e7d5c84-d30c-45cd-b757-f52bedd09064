# -*- coding: utf-8 -*-
from odoo.http import request
from odoo import api, fields, models

from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = "stock.picking"

    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
    )

    def button_validate(self):
        if not request.session.get("session_owner", False):
            raise ValidationError(self.env._("Please select an employee before validating the delivery order."))
        else:
            self.employee_id = request.session.get("session_owner", False)
            request.session["session_owner"] = False
        return super(StockPicking, self).button_validate()